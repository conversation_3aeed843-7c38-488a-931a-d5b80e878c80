<script setup>
import { isNil, pick } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { nextTick } from 'vue';
import { useModal } from 'vue-final-modal';
import BiForecastModal from '~/bi/components/chart-builder/common/bi-forecast-modal.vue';
import { useBiChartBuilderHelpers } from '~/bi/composables/bi-chart-builder-helpers.composable';
import { BI_DEFAULT_PALETTE_COLORS, CHART_TO_CATEGORY_TYPE_MAP, CHART_TO_VALUE_TYPE_MAP } from '~/bi/constants/bi-constants';
import { useBiStore } from '~/bi/store/bi.store';
import { sleep } from '~/common/utils/common.utils';

const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['seriesConfigChange', 'propertyChange', 'layoutCategorySelected']);

const bi_store = useBiStore();
const { chart_builder_columns_data_types } = storeToRefs(bi_store);
const { all_columns, stack_by_field_columns } = useBiChartBuilderHelpers();

const forecast_modal = useModal({
  component: BiForecastModal,
  attrs: {
    onClose() {
      forecast_modal.close();
    },
  },
});

const category_columns = computed(() => {
  return all_columns.value.filter((column) => {
    if (CHART_TO_CATEGORY_TYPE_MAP[props.chartType].includes(chart_builder_columns_data_types.value[column]))
      return true;
    return false;
  });
});

const stack_by_columns_for_series = computed(() => {
  return [
    {
      label: 'General',
      items: [{ value: 'none', label: 'None' }, { value: true, label: 'Series' }],
    },
    {
      label: 'Fields',
      items: stack_by_field_columns.value,
    },
  ];
});

const default_series_values = computed(() => {
  let type = props.chartType.replace('_chart', '');
  if (type === 'horizontalBar' || type === 'column' || type === 'mixed')
    type = 'bar';
  return [
    {
      value: getValueColumns()[0],
      legend: getValueColumns()[0],
      type,
      color: BI_DEFAULT_PALETTE_COLORS[0],
      y_axis: 'primary',
      line_style: 'solid',
      line_width: 2,
      line_shape: 'straight',
      prefix: '',
      suffix: '',
      stack: true,
    },
  ];
});

function getValueColumns(index = null) {
  return all_columns.value.filter((column) => {
    if (!isNil(index) && props.chartConfig?.layout_values?.some?.((item, item_index) => item.value === column && item_index !== index))
      return false;
    if (CHART_TO_VALUE_TYPE_MAP[props.chartType].includes(chart_builder_columns_data_types.value[column]))
      return true;
    return false;
  });
}

function onSeriesItemChange(payload, index) {
  emit('seriesConfigChange', payload, index);
}

function onColorChange(color) {
  emit('propertyChange', { color });
}

async function onAddSeries(index, forecast_config = null) {
  props.formInstance.elements$.layout_values.add(index);
  await nextTick();
  let type = props.chartType.replace('_chart', '');
  if (type === 'horizontalBar' || type === 'column' || type === 'mixed')
    type = 'bar';

  let root_series_styles = {};
  if (forecast_config) {
    root_series_styles = pick(props.chartConfig.layout_values.find(item => item.value === forecast_config.forecast_for_series), [
      'type',
      'color',
      'y_axis',
      'line_width',
      'line_shape',
      'prefix',
      'suffix',
      'stack',
    ]);
    root_series_styles.line_style = 'dashed';
  }

  emit(
    'seriesConfigChange',
    // Defaults
    {
      type,
      value: forecast_config?.forecast_series_name || getValueColumns(index)[0],
      color: BI_DEFAULT_PALETTE_COLORS[index % BI_DEFAULT_PALETTE_COLORS.length],
      y_axis: 'primary',
      line_style: 'solid',
      line_width: 2,
      line_shape: 'straight',
      prefix: '',
      suffix: '',
      stack: true,
      forecast_config,
      ...root_series_styles,
    },
    index,
  );
  await sleep(100);
  props.formInstance?.elements$.layout_values.children$Array[index].children$?.legend?.input?.focus();
}

async function onRemoveSeries(index) {
  const element_to_remove = props.chartConfig.layout_values[index];
  props.formInstance.elements$.layout_values.update(props.chartConfig.layout_values.filter((item) => {
    if (item.value === element_to_remove.value)
      return false;
    if (item.forecast_config?.forecast_for_series === element_to_remove.value)
      return false;
    return true;
  }));
}

async function onAddComparison(index) {
  props.formInstance.elements$.layout_values.add(index);
  await nextTick();
  props.formInstance?.elements$.layout_values.children$Array[index].children$?.comparison_number?.input?.focus();
}

function onOpenForecastModal() {
  forecast_modal.patchOptions({
    attrs: {
      mode: 'create',
      initialData: {},
      chartConfig: props.chartConfig,
      onSave(forecast_config) {
        onAddSeries(props.chartConfig.layout_values.length, forecast_config);
        forecast_modal.close();
      },
    },
  });
  forecast_modal.open();
}

function getAxis(type) {
  // type = 'category' || 'value'
  if (props.chartConfig.type === 'horizontalBar_chart') {
    if (type === 'category')
      return 'y';
    else return 'x';
  }
  else {
    if (type === 'category')
      return 'x';
    else return 'y';
  }
}
</script>

<template>
  <template v-if="['column_chart', 'horizontalBar_chart', 'line_chart', 'area_chart', 'mixed_chart', 'scatter_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
      rules="required"
      :items="category_columns"
      :default="category_columns[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
      @select="emit('layoutCategorySelected', $event)"
    />
    <ListElement
      name="layout_values"
      rules="required"
      :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
      :sort="true"
      :controls="{ add: false, remove: false, sort: true }"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
        },
      }"
      :min="1"
      :initial="1"
      :default="default_series_values"
    >
      <template #default="{ index }">
        <ObjectElement
          :name="index"
        >
          <SelectElement
            v-if="!props.chartConfig?.layout_values?.[index]?.value"
            name="value"
            rules="required"
            :native="false"
            :items="getValueColumns(index)"
            :add-classes="{
              SelectElement: {
                select: {
                  wrapper: 'ml-4',
                },
              },
            }"
            @change="onSeriesItemChange({ legend: $event }, index)"
          >
            <template #caret>
              <IconHawkXClose
                class="w-4 h-4 cursor-pointer ml-1 mr-2"
                @click="props.formInstance.elements$.layout_values.remove(index)"
              />
            </template>
          </SelectElement>
          <TextElement
            v-else
            name="legend"
            :default="props.chartConfig?.layout_values?.[index]?.legend || props.chartConfig?.layout_values?.[index]?.value"
            :placeholder="props.chartConfig?.layout_values?.[index]?.value"
            :add-classes="{
              TextElement: {
                inputContainer: 'pl-6',
              },
            }"
            @keyup.enter="onAddSeries(props.chartConfig.layout_values.length)"
          >
            <template #addon-before>
              <BiColorPicker
                type="plain"
                :active-color="props.chartConfig?.layout_values?.[index].color"
                :append-mixed-color-to-the-end="true"
                @color-selected="onSeriesItemChange({ color: $event }, index)"
              />
            </template>
            <template #addon-after>
              <BiSeriesContextMenu
                :chart-config="props.chartConfig"
                :series-config="props.chartConfig?.layout_values?.[index]"
                :columns="getValueColumns(index)"
                @field-selected="onSeriesItemChange($event, index)"
              />
              <span
                class="cursor-pointer ml-1"
                :class="{
                  '!cursor-not-allowed opacity-20': props.chartConfig?.layout_values?.length <= 1,
                }"
              >
                <IconHawkXClose
                  class="w-4 h-4"
                  :class="{
                    'pointer-events-none': props.chartConfig?.layout_values?.length <= 1,
                  }"
                  @click="onRemoveSeries(index)"
                />
              </span>
            </template>
          </TextElement>
          <HiddenElement name="type" />
          <HiddenElement name="y_axis" />
          <HiddenElement name="line_style" />
          <HiddenElement name="line_width" />
          <HiddenElement name="line_shape" />
          <HiddenElement name="prefix" />
          <HiddenElement name="suffix" />
          <HiddenElement name="stack" />
        </ObjectElement>
      </template>
      <template #after>
        <div class="flex items-center" :class="getValueColumns(props.chartConfig.layout_values?.length || 0).length ? 'justify-between' : 'justify-end'">
          <HawkButton
            v-if="getValueColumns(props.chartConfig.layout_values?.length || 0).length"
            type="link"
            size="xs"
            @click="onAddSeries(props.chartConfig.layout_values.length)"
          >
            <IconHawkPlus />
            <span>
              Add another series
            </span>
          </HawkButton>
          <HawkButton
            v-if="['column_chart', 'line_chart', 'area_chart', 'mixed_chart'].includes(props.chartType) && props.chartConfig.layout_category && chart_builder_columns_data_types[props.chartConfig.layout_category] === 'date'"
            v-tippy="{
              content: 'Add a forecast series',
              placement: 'top',
            }"
            icon
            color="gray"
            size="xxs"
            type="light"
            class="border !border-gray-200 rounded-md"
            @click="onOpenForecastModal"
          >
            <IconHawkTrendUpOne class="size-4" />
          </HawkButton>
        </div>
      </template>
    </ListElement>
    <SelectElement
      v-if="!['scatter_chart'].includes(props.chartType)"
      name="stack_by"
      label="Stack by"
      :groups="true"
      :items="stack_by_columns_for_series"
      default="none"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-else-if="['pie_chart', 'donut_chart', 'pyramid_chart', 'funnel_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      rules="required"
      label="Category"
      :items="category_columns"
      :default="category_columns[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="layout_values"
      rules="required"
      label="Value"
      :items="getValueColumns()"
      :default="getValueColumns()[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-else-if="['heatmap_chart', 'pareto_chart', 'waterfall_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_category"
      rules="required"
      label="X-axis"
      :items="category_columns"
      :default="category_columns[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <SelectElement
      name="layout_values"
      rules="required"
      label="Y-axis"
      :items="getValueColumns()"
      :default="getValueColumns()[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <template v-if="['heatmap_chart'].includes(props.chartType)">
      <SelectElement
        name="stack_by"
        rules="required"
        label="Stack by"
        :items="stack_by_field_columns"
        :default="stack_by_field_columns[0]"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
      />
    </template>
  </template>
  <template v-else-if="['gauge_chart', 'progress_chart'].includes(props.chartType)">
    <SelectElement
      name="layout_values"
      rules="required"
      label="Value"
      :items="getValueColumns()"
      :default="getValueColumns()[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <div class="flex justify-between">
      <div class="col-span-4 text-sm font-medium text-gray-700">
        Color
      </div>
      <div class="col-span-8">
        <BiColorPicker
          type="outlined"
          :active-color="props.chartConfig?.color || BI_DEFAULT_PALETTE_COLORS[0]"
          @color-selected="onColorChange"
        />
      </div>
    </div>
    <HiddenElement name="color" />
  </template>
  <BiPivotTableLayoutTab
    v-else-if="props.chartType === 'pivot_table'"
  />
  <BiTableLayoutTab
    v-else-if="props.chartType === 'table'"
  />
  <template
    v-else-if="props.chartType === 'number_chart'"
  >
    <SelectElement
      name="layout_category"
      label="Primary number"
      rules="required"
      :items="category_columns"
      :default="category_columns[0]"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ListElement
      name="layout_values"
      label="Comparisons"
      :controls="{ add: false, remove: false, sort: true }"
      :sort="true"
      :add-classes="{
        ListElement: {
          handle: 'left-8 top-[1px] visible opacity-100 !z-0',
          add: '!bg-white !border-white !text-primary-700 hover:!scale-100 !mt-0 -ml-1',
        },
      }"
      :initial="0"
    >
      <template #default="{ index }">
        <ObjectElement
          :name="index"
        >
          <SelectElement
            name="value"
            :items="getValueColumns(index)"
            :default="getValueColumns(index)[0]"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
            :add-classes="{
              SelectElement: {
                select: {
                  wrapper: 'ml-4',
                },
              },
            }"
          >
            <template #caret>
              <IconHawkXClose
                class="w-4 h-4 cursor-pointer ml-1 mr-2"
                @click.stop="props.formInstance.elements$.layout_values.remove(index)"
              />
            </template>
          </SelectElement>
        </ObjectElement>
      </template>
      <template #after>
        <HawkButton
          v-if="getValueColumns(props.chartConfig.layout_values?.length || 0).length"
          type="link"
          size="xs"
          @click="onAddComparison(props.chartConfig.layout_values.length)"
        >
          <IconHawkPlus />
          <span>
            Add another comparison
          </span>
        </HawkButton>
      </template>
    </ListElement>
  </template>
  <template v-else-if="props.chartType === 'details_card'">
    <!-- TODO v-if condition -->
    <!-- <Vueform>
      <ListElement name="fields">
        <template #default="{ index }">
          <SelectElement
            :name="index"
            :items="all_columns"
            :default="all_columns[0]"
            :native="false"
            :can-clear="false"
            :can-deselect="false"
          />
        </template>
        <template #after>
          <HawkButton
            v-if="true"
            type="link"
            size="xs"
            @click="props.formInstance.elements$.fields.add()"
          >
            <IconHawkPlus />
            <span>
              Add another comparison
            </span>
          </HawkButton>
        </template>
      </ListElement>
    </Vueform> -->
    <BiDetailsCardLayoutTab />
  </template>
  <template v-else>
    Layout - {{ props.chartType }}
  </template>
</template>
