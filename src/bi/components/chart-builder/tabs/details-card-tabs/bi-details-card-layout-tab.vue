<script setup>
import { orderBy, pick } from 'lodash-es';
import { nextTick, ref, toRaw, watch } from 'vue';
import BiColumnNameEditor from '~/bi/components/chart-builder/common/bi-column-name-editor.vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';

const draggable = defineAsyncComponent(() => import('vuedraggable'));

const bi_store = useBiStore();

const current_dragged_element = ref(null);

// Add this ref to track columns directly
const localColumns = ref([]);

// Watch the store and update local columns
watch(() => pick(bi_store.widget_builder_config.chart, ['fields_map', 'field_groups']), (newChart) => {
  const { fields_map = {}, field_groups = [] } = newChart;
  const list = orderBy([...Object.values(fields_map), ...field_groups], 'order_index');

  localColumns.value = list.reduce((acc, col) => {
    if (col.type === 'group') {
      acc.list.push({
        ...col,
        columns: col.columns.map(key => fields_map[key]).filter(Boolean),
      });
      col.columns.forEach((val) => {
        acc.added_columns_map[val] = true;
      });
    }
    else if (!acc.added_columns_map[col.key]) {
      acc.list.push(col);
      acc.added_columns_map[col.key] = true;
    }
    return acc;
  }, { list: [], added_columns_map: {} }).list;
}, { immediate: true, deep: true });

// Function to update store from local columns
function updateStore() {
  const { fields_map, field_groups } = localColumns.value.reduce((acc, col, index) => {
    const order_index = 1000 * (index + 1);
    if (col.type === 'group') {
      acc.field_groups.push({
        key: col.key,
        label: col.label,
        type: col.type,
        columns: col.columns.map(val => val.key),
        order_index,
      });

      col.columns.forEach((nested_col, col_index) => {
        acc.fields_map[nested_col.key] = {
          ...nested_col,
          order_index: order_index + col_index + 1,
        };
      });
    }
    else {
      acc.fields_map[col.key] = {
        ...col,
        order_index,
      };
    }
    return acc;
  }, { field_groups: [], fields_map: {} });

  bi_store.widget_builder_config.chart.fields_map = fields_map;
  bi_store.widget_builder_config.chart.field_groups = field_groups;
  bi_store.table_widget_config_change_detector = true;
}

function updateColumnVisibility(column) {
  bi_store.widget_builder_config.chart.fields_map[column.key].visible = !column.visible;
  bi_store.table_widget_config_change_detector = true;
}

function renameField(new_name, field) {
  bi_store.widget_builder_config.chart.fields_map[field.key].label = new_name;
  bi_store.table_widget_config_change_detector = true;
}

function updateTableConfig(key, value) {
  bi_store.widget_builder_config.chart[key] = value;
  bi_store.table_widget_config_change_detector = true;
}

function handleAddGroup() {
  const newGroup = {
    key: crypto.randomUUID(),
    label: '',
    columns: [],
    type: 'group',
  };

  localColumns.value = [...localColumns.value, newGroup];
}

function handleDeleteGroup(group) {
  const current_columns = [...localColumns.value];

  const group_index = current_columns.findIndex(col => col.key === group.key);

  if (group_index !== -1) {
    const group_columns = current_columns[group_index].columns || [];
    current_columns.splice(group_index, 1, ...group_columns);
  }

  localColumns.value = current_columns;
  updateStore();
}

function getDropGroup(type) {
  return {
    name: 'fields',
    pull: true,
    put: type === 'parent' ? true : allowDrop,
    revertClone: false,
  };
}

function onDragChange(evt) {
  // Force immediate update to prevent duplicates
  if (evt.added || evt.removed) {
    nextTick(() => {
      localColumns.value = [...localColumns.value];
    });
  }
}
function onDragStart(evt) {
  current_dragged_element.value = evt.item.__draggable_context?.element || null;
}
function onDragEnd() {
  current_dragged_element.value = null;
  updateStore();
}
function allowDrop() {
  return !(current_dragged_element.value?.type === 'group');
}
</script>

<template>
  <div>
    <div class="font-semibold mb-3 text-sm">
      {{ $t('Columns') }}
    </div>
    <draggable
      v-model="localColumns"
      :group="getDropGroup('parent')"
      item-key="key" handle=".move" class="min-h-[50px] gap-y-1 mb-6"
      @start="onDragStart"
      @end="onDragEnd"
      @change="onDragChange"
    >
      <template #item="{ element }">
        <div v-if="element && element.key && element.type === 'group'">
          <div class="font-semibold mb-3 text-sm">
            <BiColumnNameEditor
              :is-field="false"
              :hide-update-visibility="true"
              :field="element"
              @rename="element.label = $event; updateStore()"
            >
              <template #right_section>
                <div class="cursor-pointer ml-2" @click="handleDeleteGroup(element)">
                  <IconHawkTrashThree class="text-gray-500 w-4.5 h-4.5" />
                </div>
              </template>
            </BiColumnNameEditor>
          </div>
          <draggable
            v-model="element.columns"
            :group="getDropGroup('nested')"
            item-key="key"
            handle=".move"
            class="min-h-[50px] gap-y-1 mb-6 ml-6"
            @start="onDragStart"
            @end="onDragEnd"
            @change="onDragChange"
          >
            <template #item="{ element: column }">
              <BiColumnNameEditor
                :field="column"
                :placeholder="column.key"
                @rename="renameField($event, column)"
                @update-visibility="updateColumnVisibility(column)"
              />
            </template>
          </draggable>
        </div>
        <BiColumnNameEditor
          v-else-if="element"
          :field="element"
          :placeholder="element.key"
          @rename="renameField($event, element)"
          @update-visibility="updateColumnVisibility(element)"
        />
      </template>
    </draggable>

    <HawkButton type="link" class="mb-3" @click="handleAddGroup()">
      <IconHawkPlus />
      <span>
        {{ $t('Add group') }}
      </span>
    </HawkButton>
  </div>
</template>
