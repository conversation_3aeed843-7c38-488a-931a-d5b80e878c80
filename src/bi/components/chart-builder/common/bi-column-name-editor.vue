<script setup>
import { onMounted } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  field: {
    type: Object,
    required: true,
  },
  hideUpdateVisibility: {
    type: Boolean,
    default: false,
  },
  isField: {
    type: Boolean,
    default: true,
  },
  placeholder: {
    type: String,
    default: 'Enter name',
  },
});

const emit = defineEmits(['rename', 'updateVisibility']);

const bi_store = useBiStore();

const is_renaming = ref(null);
const rename_value = ref('');
const rename_error = ref(false);

const { getIconsForType } = useBIQueryBuilder();

function getFieldIconType(alias) {
  const field = bi_store.alias_to_column_mapping()[alias];
  return field?.is_aggregation ? 'function' : field?.field_type;
}

function renameField() {
  if (rename_error.value)
    return;
  emit('rename', rename_value.value);
  nextTick(() => {
    const rename_field_alias = document.getElementById(`rename_field_alias_${props.field.key}`);
    rename_field_alias.blur();
  });
  is_renaming.value = null;
}

function getLabel() {
  if (props.isField) {
    return props.field.label || props.field.key;
  }
  else {
    return props.field.label;
  }
}

function checkValidName() {
  rename_error.value = !rename_value.value?.trim()?.length;
}

function updateEdit() {
  is_renaming.value = props.field.key;
}

function handleRevert() {
  rename_error.value = false;
  is_renaming.value = null;
  rename_value.value = getLabel();
  nextTick(() => {
    const rename_field_alias = document.getElementById(`rename_field_alias_${props.field.key}`);
    rename_field_alias.blur();
  });
}

onMounted(() => {
  rename_value.value = getLabel();
  if (rename_value.value?.length === 0) {
    const rename_field_alias = document.getElementById(`rename_field_alias_${props.field.key}`);
    rename_field_alias.focus();
  }
});
</script>

<template>
  <div class="">
    <div v-click-outside="() => handleRevert()" class="p-2 rounded-md group my-2.5 border" :class="[rename_error ? 'border-red-500' : is_renaming === field.key ? 'border-gray-500' : 'border-gray-200 hover:bg-gray-50']">
      <div class="flex items-center justify-between">
        <div class="flex items-center flex-1 min-w-0">
          <div class="pr-3 move cursor-move flex-shrink-0">
            <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
          </div>
          <div class="flex items-center flex-1 min-w-0">
            <div v-if="isField" :class="getFieldIconType(field.key) === 'function' ? 'border-l-4 border-warning-300' : ''">
              <component :is="getIconsForType(getFieldIconType(field.key))" class="text-gray-600 w-4 h-4 flex-shrink-0" />
            </div>
            <input
              :id="`rename_field_alias_${field.key}`"
              v-model="rename_value"
              :placeholder="placeholder"
              type="text"
              class="text-sm text-gray-700 cursor-text flex-1 bg-transparent"
              :class="{ 'pl-2': isField }"
              @focus="updateEdit()"
              @input="checkValidName"
              @keyup.enter="renameField()"
              @keyup.escape="handleRevert()"
            >
          </div>
        </div>
        <div v-if="is_renaming === field.key" class="flex items-center ml-2 flex-shrink-0">
          <div class="text-gray-500  mr-2 cursor-pointer" @click.stop="handleRevert()">
            <IconHawkXClose class="size-4" />
          </div>
          <div class="text-gray-500 mr-2 cursor-pointer" :class="{ 'pointer-events-none opacity-50': rename_error }" @click.stop="renameField()">
            <IconHawkCheck class="size-4" />
          </div>
        </div>
        <div v-else-if="!props.hideUpdateVisibility" class="cursor-pointer text-gray-600 ml-2 flex-shrink-0" @click.stop="emit('updateVisibility')">
          <IconHawkEyeTwo v-if="field.visible" class="h-4 w-4" />
          <IconHawkEyeOff v-else class="h-4 w-4" />
        </div>
        <div v-else class="ml-2 flex-shrink-0">
          <slot name="right_section" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
