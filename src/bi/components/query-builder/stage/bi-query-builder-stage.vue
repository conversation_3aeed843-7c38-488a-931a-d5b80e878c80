<script setup>
import { storeToRefs } from 'pinia';
import { useModal } from 'vue-final-modal';

import BiFilterList from '~/bi/components/filters/bi-filter-list/bi-filter-list.vue';
import BIFilterTrigger from '~/bi/components/filters/bi-filter-trigger.vue';
import biQueryBuilderFieldSettings from '~/bi/components/query-builder/stage/bi-query-builder-field-settings.vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';
import { useBiStore } from '~/bi/store/bi.store';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => [],
  },
  stageIndex: {
    type: Number,
  },
  previousStageFields: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    type: Object,
    default: () => ({
      columns: [],
      orderBy: [],
      limit: null,
    }),
  },
});

const emit = defineEmits(['update:modelValue', 'fieldsDeleted', 'fieldsAdded', 'rename']);

const bi_store = useBiStore();
const { widget_builder_config, all_schema_columns } = storeToRefs(bi_store);
const { stages } = toRefs(widget_builder_config.value.query);

const has_row_limit = ref(false);
const is_expression_dropdown_open = ref(false);
const selected_expression = ref(null);
const { getIconsForType, aggregations_map } = useBIQueryBuilder();
const { $t } = useCommonImports();
const focused = ref(-1);
const draggable = defineAsyncComponent(() => import('vuedraggable'));

const localColumns = computed({
  get: () => props.modelValue.columns || [],
  set: v => updateModelValue({ columns: v }),
});

const localOrderBy = computed({
  get: () => props.modelValue.orderBy || [],
  set: v => updateModelValue({ orderBy: v }),
});

const localFilters = computed({
  get: () => props.modelValue.filters || [],
  set: v => updateModelValue({ filters: v }),
});

const { open: openFieldSettings, close: closeFieldSettings, patchOptions } = useModal({
  component: biQueryBuilderFieldSettings,
});

function updateSettings(field) {
  patchOptions({
    attrs: {
      field,
      stage: props.modelValue,
      previous_stage_columns: props.previousStageFields,
      onClose() {
        closeFieldSettings();
      },
      onSave(event) {
        field.partitionBy = event.partitionBy;
        field.orderBy = event.orderBy;
        field.frame = event.frame;
        closeFieldSettings();
        emit('fieldsAdded');
      },
    },
  });
  openFieldSettings();
}

function onColumnsSelected(field) {
  if (props.modelValue.columns.find(f => f.expr ? false : (f.alias === field.alias)))
    return;
  updateModelValue({ columns: [...props.modelValue.columns, field] });
  emit('fieldsAdded');
}

function removeField(index) {
  const remaining_fields = props.modelValue.columns.filter((_, i) => i !== index);
  updateModelValue({ columns: remaining_fields });
  emit('fieldsDeleted');
}

function saveExpression(expression) {
  if (props.modelValue.columns.find(f => f.expr === expression.expr))
    return;
  updateModelValue({ columns: [...props.modelValue.columns, expression] });
  is_expression_dropdown_open.value = false;
  emit('fieldsAdded');
}

function updateExpression(expression) {
  const index = props.modelValue.columns.findIndex(f => f.label === expression.label);
  const columns = props.modelValue.columns;
  columns[index] = expression;
  updateModelValue({ columns });
  selected_expression.value = null;
}

function onSortSelected(field) {
  if (props.modelValue.orderBy.find(f => f.column === field.alias))
    return;
  field.direction = field.direction === 'asc' ? 'desc' : 'asc';
  field.column = field.alias;
  const sort_field = { direction: field.direction, column: field.alias };
  updateModelValue({ orderBy: [...props.modelValue.orderBy, sort_field] });
}

function onFilterApply(filter_config) {
  updateModelValue({ filters: [...props.modelValue.filters, filter_config] });
}

function removeFilterField(index) {
  updateModelValue({ filters: props.modelValue.filters.filter((_, i) => i !== index) });
}

function editAppliedFilter(filter_config, index, close = () => {}) {
  const updated_filter_fields = [...props.modelValue.filters];
  updated_filter_fields[index] = filter_config;
  updateModelValue({ filters: updated_filter_fields });
  close?.();
}

function removeSortField(index) {
  updateModelValue({ orderBy: props.modelValue.orderBy.filter((_, i) => i !== index) });
}

function updateModelValue({ columns, orderBy, limit, filters }) {
  const limit_value = limit === undefined ? props.modelValue.limit : limit;
  emit('update:modelValue', {
    columns: columns || props.modelValue.columns,
    orderBy: orderBy || props.modelValue.orderBy,
    filters: filters || props.modelValue.filters,
    limit: has_row_limit.value ? limit_value : null,
  });
}

function onLimitInput(event) {
  const raw = event.target.value;
  // Empty string means cleared input -> null limit
  if (raw === '') {
    updateModelValue({ limit: null });
    return;
  }
  // Convert to number; if invalid, ignore the input
  const numeric = Number(raw);
  if (Number.isNaN(numeric))
    return;
  updateModelValue({ limit: numeric });
}

function openExpressionEditor(field) {
  selected_expression.value = field;
}

const is_renaming = ref(null);
const rename_value = ref('');
const rename_error = ref(false);
function renameField(alias) {
  if (rename_error.value)
    return;
  emit('rename', { from: alias, to: rename_value.value });
  is_renaming.value = null;
}

const all_columns = computed(() => stages.value.map(stage => stage.columns).flat());
function uniqueFieldName() {
  const renamed_field = localColumns.value[is_renaming.value];
  const list = all_columns.value.map(({ alias }) => alias).filter(alias => alias !== renamed_field?.alias);
  const selected_table = stages.value[0].table;
  const selected_table_columns = all_schema_columns.value[selected_table];
  selected_table_columns.forEach(column => list.push(column.alias));
  if (list.length > 1 && rename_value.value) {
    const is_match = list.filter(name => name === rename_value.value?.trim());
    if (is_match.length > 0)
      rename_error.value = true;
    else
      rename_error.value = false;
  }
  else {
    rename_error.value = false;
  }
}

function enableRenameOption(field, index) {
  rename_value.value = field.alias;
  is_renaming.value = index;
  nextTick(() => {
    const rename_field_alias = document.getElementById('rename_field_alias');
    rename_field_alias.focus();
  });
}

const date_bins = [{ label: 'None', uid: null }, { label: 'Week', uid: 'week' }, { label: 'Month', uid: 'month' }, { label: 'Quarter', uid: 'quarter' }, { label: 'Year', uid: 'year' }];
const timestamp_bins = [{ label: 'None', uid: null }, { label: 'Minute', uid: 'minute' }, { label: 'Hour', uid: 'hour' }, { label: 'Day', uid: 'day' }, { label: 'Week', uid: 'week' }, { label: 'Month', uid: 'month' }, { label: 'Quarter', uid: 'quarter' }, { label: 'Year', uid: 'year' }];

function updateDateBin(bin, field) {
  const old_alias = field.alias;
  const old_bin = field.dateBin;
  if (bin) {
    field.dateBin = bin;
    field.is_bin = true;
    const bin_label = timestamp_bins.find(_bin => _bin.uid === bin)?.label;
    const newLabel = `(${bin_label})`;
    const prevBinLabel = timestamp_bins.find(_bin => _bin.uid === old_bin)?.label;
    const regex = new RegExp(`\\(${prevBinLabel}\\)`, 'i');
    if (!old_bin && field.dateBin) {
      field.alias += ` ${newLabel}`;
    }
    else if (regex.test(field.alias)) {
      field.alias = field.alias.replace(regex, newLabel);
    }
    stages.value.forEach((stage, index) => {
      if (props.stageIndex >= index)
        return;
      stage.columns.forEach((column) => {
        if (column.alias === old_alias && column.oldDateBin === old_bin) {
          column.oldDateBin = field.dateBin;
        }
      });
    });
  }
  else {
    field.dateBin = undefined;
    field.is_bin = undefined;
    field.alias = field.alias.replace(/\((?:Minute|Hour|Day|Week|Quarter|Year)\)$/, '');
  }
  if (old_alias !== field.alias)
    emit('rename', { from: old_alias, to: field.alias });
}

function updateNumberBinWidth(e, field) {
  const old_alias = field.alias;
  const old_bin = field.binWidth;
  const raw = e.target.value;
  const digits = String(raw).replace(/\D/g, '');
  e.target.value = digits;
  if (digits === '') {
    return e.preventDefault();
  }
  const numeric = Number(digits);
  if (Number.isNaN(numeric))
    return e.preventDefault();
  field.binWidth = numeric;
  if (new RegExp(`\\(${old_bin}\\)$`).test(field.alias)) {
    const newLabel = `(${digits})`;
    field.alias = field.alias.replace(new RegExp(`\\(${old_bin}\\)$`), newLabel);
  }
  if (old_alias !== field.alias)
    emit('rename', { from: old_alias, to: field.alias });
}
</script>

<template>
  <div class="w-full">
    <div>
      <draggable v-model="localColumns" item-key="alias" tag="div" handle=".move" :animation="150">
        <template #item="{ element: field, index }">
          <div :class="{ 'pointer-events-none': focused !== -1 && focused !== index }">
            <div v-if="is_renaming === index" v-click-outside="() => is_renaming = null" class="flex items-center justify-between gap-2 py-2 pl-6 cursor-pointer hover:bg-gray-50 rounded-lg relative group border  rounded-md" :class="{ 'border-red-500': rename_error, 'border-gray-400': !rename_error }">
              <span :class="{ 'border-l-4 border-warning-300 -ml-1': field.agg }">
                <component :is="getIconsForType(field.agg ? 'function' : field.field_type)" class="text-gray-600 size-4 " />
              </span>
              <input id="rename_field_alias" v-model="rename_value" type="text" class="text-sm text-gray-700 cursor-text w-full bg-transparent" @input="uniqueFieldName" @keyup.enter="renameField(field.alias)" @keyup.escape="is_renaming = null">
              <div class="flex items-center">
                <div class="text-gray-500  mr-2" @click.stop="is_renaming = null;">
                  <IconHawkXClose class="size-4" />
                </div>
                <div class="text-gray-500  mr-2" :class="{ 'pointer-events-none opacity-50': rename_error }" @click.stop="renameField(field.alias)">
                  <IconHawkCheck class="size-4" />
                </div>
              </div>
            </div>
            <div v-else :key="field.alias" :class="{ 'opacity-50': field.skip }" class="flex items-center justify-between gap-2 py-2 cursor-pointer hover:bg-gray-50 rounded-lg relative group" @mousedown="is_renaming = null" @click="field.expr ? openExpressionEditor(field) : null">
              <div class="flex items-center">
                <div class="pr-3 move cursor-move invisible group-hover:visible">
                  <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
                </div>
                <span v-if="field.expr" :class="{ 'border-l-4 border-warning-300 -ml-1': field.type === 'aggregation' }">
                  <component :is="getIconsForType(field.type === 'aggregation' ? 'function' : 'formula')" class="text-gray-600 size-4 mr-2" />
                </span>
                <span v-else :class="{ 'border-l-4 border-warning-300 -ml-1': field.agg }">
                  <component :is="getIconsForType(field.agg ? 'function' : field.field_type)" class="text-gray-600 size-4 mr-2" />
                </span>
                <span class="text-sm text-gray-700 cursor-text" @click="field.expr ? null : enableRenameOption(field, index)">{{ field.alias }}</span>
              </div>
              <div
                class="flex items-center" @focusin="focused = index"
                @focusout="focused = -1"
              >
                <div v-if="aggregations_map[field?.agg]?.type === 'window'">
                  <IconHawkSettingsOne class="size-4 text-gray-500 hidden group-hover:block group-focus-within:block  mr-2" :class="{ '!text-primary-500': field.frame }" @click="updateSettings(field)" />
                </div>
                <div v-if="field.field_type === 'date' || field.field_type === 'timestamp'" class="hidden group-focus-within:block  group-hover:block mr-2 h-5" :class="{ 'pointer-events-all': focused === index }">
                  <hawk-menu position="fixed" :items="field.field_type === 'date' ? date_bins : timestamp_bins" additional_trigger_classes="!ring-0 !focus:ring-0 !px-2 !py-0" @select="updateDateBin($event.uid, field)">
                    <template #trigger="{ open }">
                      <div class="border-b flex items-center text-sm text-gray-600">
                        {{ field.dateBin ? timestamp_bins.find(_bin => _bin.uid === field.dateBin)?.label : $t('None') }} <IconHawkChevronDown class="size-4 text-gray-500 ml-2" :class="{ 'rotate-180': open }" />
                      </div>
                    </template>
                  </hawk-menu>
                </div>
                <div v-if="field.field_type === 'integer' && field.binWidth" class="hidden  group-focus-within:block group-hover:block mr-2  h-5" :class="{ 'pointer-events-all': focused === index }">
                  <input :value="field.binWidth" placeholder="Bin" class="w-16 border border-gray-300 rounded-sm outline-gray-400 px-2" @keypress.enter="e => updateNumberBinWidth(e, field)">
                </div>
                <div class="text-gray-500 hidden group-hover:block group-focus-within:block mr-2" @click.stop="field.skip = !field.skip; field.skip ? emit('fieldsDeleted', field) : emit('fieldsAdded', field)">
                  <IconHawkEye v-if="!field.skip" v-tippy="{ content: $t('Hide the column from the final result to avoid unnecessary grouping. Useful for creating a temporary column for complex custom expressions.'), placement: 'bottom' }" class="size-4" />
                  <IconHawkEyeOff v-else v-tippy="{ content: $t('Show the column in the final result.'), placement: 'bottom' }" class="size-4" />
                </div>
                <div class="text-gray-500 hidden group-hover:block group-focus-within:block mr-2" @click.stop="removeField(index)">
                  <IconHawkXClose class="size-4" />
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>
      <div>
        <bi-query-builder-add-column-dropdown :tables="tables" :stage-index="stageIndex" :previous-stage-fields="previousStageFields" @selected="onColumnsSelected" @expression="is_expression_dropdown_open = true;" />
        <bi-query-builder-expression-editor v-if="is_expression_dropdown_open" :tables="tables" :previous-stage-fields="previousStageFields" :fields="modelValue.columns" @save="saveExpression" @close="is_expression_dropdown_open = false" />
      </div>
      <div v-if="modelValue.orderBy.length > 0" class="border-t py-2 mt-2">
        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center">
            <IconHawkSwitchVerticalOne class="mr-2" />
            {{ $t('Sort') }}
          </div>
          <bi-query-builder-sort-menu :tables="tables" :stage-index="stageIndex" :previous-stage-fields="previousStageFields" :fields="modelValue.columns" @selected="onSortSelected">
            <template #default="{ open }">
              <Hawk-button
                size="xxs" color="gray" type="light" icon :class="{
                  '!bg-gray-700 !text-white': open,
                }"
              >
                <IconHawkPlus class="size-4" />
              </Hawk-button>
            </template>
          </bi-query-builder-sort-menu>
        </div>
        <draggable v-model="localOrderBy" item-key="column" tag="div" handle=".move" :animation="150">
          <template #item="{ element: field, index }">
            <div :key="field.column" class="flex items-center justify-between gap-2 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative group" @click="field.direction = field.direction === 'asc' ? 'desc' : 'asc'">
              <div class="flex items-center">
                <div class="pr-3 move cursor-move invisible group-hover:visible">
                  <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" />
                </div>
                <component :is="getIconsForType(field.direction || 'asc')" class="text-gray-600 size-4 mr-2" />
                <span class="text-sm text-gray-700">{{ field.column }}</span>
              </div>
              <div class="text-gray-500 hidden group-hover:block mr-2" @click.stop="removeSortField(index)">
                <IconHawkXClose class="size-4" />
              </div>
            </div>
          </template>
        </draggable>
      </div>
      <!-- Filter List -->
      <div v-if="modelValue.filters.length > 0" class="border-t py-2 mt-2">
        <!-- {{ modelValue.filters }} -->

        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center">
            <IconHawkFilterFunnelOne class="mr-2" />
            {{ $t('Filter') }}
          </div>
          <BIFilterTrigger
            :tables="tables"
            :fields="modelValue.columns"
            :previous-stage-fields="previousStageFields"
            :stage-index="stageIndex"
            @apply="onFilterApply"
          >
            <template #trigger_label>
              <IconHawkPlus class="size-3.5" />
            </template>
          </BIFilterTrigger>
        </div>
        <BiFilterList
          :fields="modelValue.columns"
          :previous-stage-fields="previousStageFields" :tables="tables" :existing-column-filters="localFilters" @remove-filter-field="removeFilterField" @edit-applied-filter="editAppliedFilter" @reorder="(list) => updateModelValue({ filters: list })"
        />
      </div>
      <div v-if="has_row_limit" class="mt-2 p-2 border-t">
        <div class="text-gray-600 w-full text-sm font-medium flex items-center justify-between mb-2">
          <div class="flex items-center w-[150px]">
            <IconHawkList class="mr-2" />
            {{ $t('Row Limit') }}
          </div>
          <input :value="modelValue.limit" type="number" inputmode="numeric" pattern="\d*" placeholder="Enter row limit" class="block w-full rounded-lg border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-500 focus:ring-1 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 pl-4" @input="onLimitInput($event)">
        </div>
      </div>
      <div>
        <div class="flex items-center justify-between">
          <div />
          <div class="flex items-center gap-2">
            <!-- Filter -->
            <BIFilterTrigger
              :tables="tables"
              :fields="modelValue.columns"
              :previous-stage-fields="previousStageFields"
              :stage-index="stageIndex"
              @apply="onFilterApply"
            >
              <template #trigger_label>
                <IconHawkFilterFunnelOne class="size-3.5" />
              </template>
            </BIFilterTrigger>
            <bi-query-builder-sort-menu
              v-slot="{ open }"
              :tables="tables" :fields="modelValue.columns" :previous-stage-fields="previousStageFields" :stage-index="stageIndex" @selected="onSortSelected"
            >
              <Hawk-button
                size="xxs" color="gray" type="light" class="border !border-gray-200 rounded-md"
                :class="{
                  '!bg-gray-700 !text-white': open,
                }"
                icon
              >
                <IconHawkSwitchVerticalOne class="size-3.5" />
              </Hawk-button>
            </bi-query-builder-sort-menu>
            <Hawk-button
              size="xxs" color="gray" type="light" class="border !border-gray-200 !rounded-md !ring-transparent"
              :class="{
                '!bg-gray-700 !text-white': has_row_limit,
              }"
              icon @click="has_row_limit = !has_row_limit; updateModelValue({ limit: null })"
            >
              <IconHawkList class="size-3.5" />
            </Hawk-button>
          </div>
        </div>
        <bi-query-builder-expression-editor v-if="selected_expression" :tables="tables" :fields="modelValue.columns" :previous-stage-fields="previousStageFields" :value="selected_expression" @save="updateExpression" @close="selected_expression = null" @rename="emit('rename', $event)" />
      </div>
    </div>
  </div>
</template>
