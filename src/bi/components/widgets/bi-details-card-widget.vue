<script setup>
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    required: true,
  },
  isPreview: {
    type: Boolean,
    required: true,
  },
});

const state = reactive({
  current_row: 0,
});

const current_row_data = computed(() => props.data[state.current_row] || {});

const current_row_data_with_columns_ordering = computed(() => {
  return Object.entries(current_row_data.value).sort((a, b) => {
    return (props.config.chart.fields_map?.[a[0]]?.order_index || 0) - (props.config.chart.fields_map?.[b[0]]?.order_index || 0);
  }).filter(([key]) => props.config.chart.fields_map?.[key]?.visible);
});

function nextRow() {
  state.current_row = (state.current_row + 1) % props.data.length;
}

function previousRow() {
  state.current_row = (state.current_row - 1 + props.data.length) % props.data.length;
}
</script>

<template>
  <div
    class="max-w-4xl mx-auto"
  >
    <div
      class="grid gap-4 sm:gap-6"
      style="grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));"
    >
      <div
        v-for="([key, value], index) in current_row_data_with_columns_ordering"
        :key="index"
        class="flex flex-col rounded-xl p-3"
      >
        <span class="text-sm font-medium text-gray-700">
          {{ key }}
        </span>
        <span class="text-base text-gray-900 truncate">
          {{ value || '-' }}
        </span>
      </div>
    </div>

    <div
      class="flex justify-end mt-6 items-center gap-3 text-sm text-gray-600 select-none"
    >
      <IconHawkChevronLeft
        class="cursor-pointer hover:text-gray-900 transition"
        @click="previousRow"
      />
      <span class="font-medium">
        {{ state.current_row + 1 }} / {{ props.data.length }}
      </span>
      <IconHawkChevronRight
        class="cursor-pointer hover:text-gray-900 transition"
        @click="nextRow"
      />
    </div>
  </div>
</template>
