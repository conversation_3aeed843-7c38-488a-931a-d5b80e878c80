<script setup>
import { orderBy } from 'lodash-es';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    required: true,
  },
  isPreview: {
    type: Boolean,
    required: true,
  },
});

const state = reactive({
  current_row: 0,
  collapsedGroups: new Set(),
});

const current_row_data = computed(() => props.data[state.current_row] || {});

// Organize data into groups and individual fields
const organized_data = computed(() => {
  const { fields_map = {}, field_groups = [] } = props.config.chart;
  const current_data = current_row_data.value;

  // Create a list of all items (groups and individual fields) ordered by order_index
  const all_items = orderBy([...Object.values(fields_map), ...field_groups], 'order_index');

  const result = [];
  const processed_fields = new Set();

  for (const item of all_items) {
    if (item.type === 'group') {
      // Process group
      const group_fields = item.columns
        .map(key => ({
          key,
          value: current_data[key],
          label: fields_map[key]?.label || key,
          visible: fields_map[key]?.visible,
        }))
        .filter(field => field.visible);

      if (group_fields.length > 0) {
        result.push({
          type: 'group',
          key: item.key,
          label: item.label,
          fields: group_fields,
        });
      }

      // Mark these fields as processed
      item.columns.forEach(key => processed_fields.add(key));
    }
    else if (!processed_fields.has(item.key) && item.visible && Object.prototype.hasOwnProperty.call(current_data, item.key)) {
      // Process individual field
      result.push({
        type: 'field',
        key: item.key,
        value: current_data[item.key],
        label: item.label || item.key,
      });
      processed_fields.add(item.key);
    }
  }

  return result;
});

function toggleGroupCollapse(groupKey) {
  if (state.collapsedGroups.has(groupKey)) {
    state.collapsedGroups.delete(groupKey);
  }
  else {
    state.collapsedGroups.add(groupKey);
  }
}

function isGroupCollapsed(groupKey) {
  return state.collapsedGroups.has(groupKey);
}

function nextRow() {
  state.current_row = (state.current_row + 1) % props.data.length;
}

function previousRow() {
  state.current_row = (state.current_row - 1 + props.data.length) % props.data.length;
}
</script>

<template>
  <div class="mx-auto">
    <div class="space-y-6">
      <!-- Iterate through organized data (groups and individual fields) -->
      <template v-for="item in organized_data" :key="item.key">
        <!-- Group Item -->
        <div v-if="item.type === 'group'" class="space-y-3">
          <!-- Group Header -->
          <div class="border border-gray-200 rounded-lg bg-gray-50 p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div
                  class="cursor-pointer transition-transform duration-200"
                  :class="{ 'rotate-90': !isGroupCollapsed(item.key) }"
                  @click="toggleGroupCollapse(item.key)"
                >
                  <IconHawkChevronRight class="text-gray-500 w-4 h-4" />
                </div>
                <h3 class="text-sm font-semibold text-gray-800">
                  {{ item.label || 'Untitled Group' }}
                </h3>
              </div>
            </div>
          </div>

          <!-- Group Content (Collapsible) -->
          <div v-if="!isGroupCollapsed(item.key)" class="ml-6 border-l-2 border-gray-100 pl-4">
            <div
              class="grid gap-4 sm:gap-6"
              style="grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));"
            >
              <div
                v-for="field in item.fields"
                :key="field.key"
                class="flex flex-col"
              >
                <span class="text-sm font-medium text-gray-700">
                  {{ field.label }}
                </span>
                <span class="text-base text-gray-900 truncate">
                  {{ field.value || '-' }}
                </span>
              </div>
            </div>
          </div>

          <!-- Horizontal separator after group -->
          <div class="border-b border-gray-200" />
        </div>

        <!-- Individual Field Item -->
        <div v-else-if="item.type === 'field'" class="flex flex-col">
          <span class="text-sm font-medium text-gray-700">
            {{ item.label }}
          </span>
          <span class="text-base text-gray-900 truncate">
            {{ item.value || '-' }}
          </span>
        </div>
      </template>
    </div>

    <!-- Navigation Controls -->
    <div
      class="flex justify-end mt-6 items-center gap-3 text-sm text-gray-600 select-none"
    >
      <IconHawkChevronLeft
        class="cursor-pointer hover:text-gray-900 transition"
        @click="previousRow"
      />
      <span class="font-medium">
        {{ state.current_row + 1 }} / {{ props.data.length }}
      </span>
      <IconHawkChevronRight
        class="cursor-pointer hover:text-gray-900 transition"
        @click="nextRow"
      />
    </div>
  </div>
</template>
