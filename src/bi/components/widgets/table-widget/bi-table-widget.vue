<script setup>
import { cloneDeep, get, orderBy } from 'lodash-es';
import { computed, onMounted, reactive, watch } from 'vue';
import BiHandsontable from '~/bi/components/widgets/table-widget/bi-handsontable.vue';
import { useBiTableHelper } from '~/bi/components/widgets/table-widget/composables/bi-table-helper.composables.js';
import { useBiStore } from '~/bi/store/bi.store';
import useEmitter from '~/common/composables/useEmitter';

const props = defineProps({
  id: {
    type: String,
    default: () => crypto.randomUUID(),
  },
  data: {
    type: Array,
    default: null,
  },
  widgetBuilderMetadata: {
    type: Object,
    default: () => ({}),
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  isBuilder: {
    type: Boolean,
    default: true,
  },
  isPreview: {
    type: Boolean,
    default: true,
  },
  height: {
    type: Number,
    default: 0,
  },
});

const emitter = useEmitter();

const bi_store = useBiStore();
const {
  getCellFormatting,
  autoFitColumns,
  generateNestedTableHeaders,
  generateTableGroupHeaders,
  generateHandsontableData,
  getTableColumns,
  getCustomCellContent,
} = useBiTableHelper();

const state = reactive({
  loading: false,
  error: null,
  data: [],
  columns: [],

  table_columns: [],
  table_data: [],
  nested_headers: [],
  nested_rows: false,
  column_config: {},

  table_instance: null,

  changes_detected: false,
  loading_changes: false,
  force_update: 1, // Will be updated in the future to update settings
});

const chart_config = computed(() => {
  const config_data = props.config?.chart || {};
  const {
    type,

    columns_map = {},
    column_groups = [],
    show_row_headers = false,

    pivot_rows = [],
    pivot_columns = [],
    pivot_values = [],
    show_row_totals = false,
    show_column_totals = false,
    show_grand_totals = false,

    conditional_formatting = [],
  } = config_data;

  if (type === 'table') {
    return { type, columns_map, column_groups, conditional_formatting, show_row_headers };
  }
  else if (type === 'pivot_table') {
    return { type, pivot_rows, pivot_columns, pivot_values, show_row_totals, show_grand_totals, show_column_totals, conditional_formatting };
  }
  return config_data;
});
const enable_row_headers = computed(() => {
  if (chart_config.value?.type === 'table')
    return chart_config.value?.show_row_headers || false;
  return false;
});
const table_height = computed(() => {
  if (props.height)
    return props.height;
  // Header and Footer is about 85 each total 190
  // Additional table padding 40
  const available_height = window.innerHeight - 220;
  return available_height;
});
const columns_data_map = computed(() => {
  return (props.widgetBuilderMetadata?.columns || []).reduce((acc, col) => {
    acc[col.name] = col;
    return acc;
  }, {});
});

// -------------------------------- Methods --------------------------------- //

async function setupPivotTable(reinitialize = true) {
  if (props.isPreview && reinitialize) {
    const new_columns = (props.widgetBuilderMetadata?.columns || []).map(col => col.name);
    const alias_map = bi_store.alias_to_column_mapping();
    const keys_map = new_columns.reduce((acc, curr) => {
      if (alias_map[curr]?.is_aggregation) {
        acc.value_keys.push(curr);
      }
      else {
        acc.non_value_keys.push(curr);
      }
      acc[curr] = curr;
      return acc;
    }, { value_keys: [], non_value_keys: [] });
    const half = Math.ceil(keys_map.non_value_keys.length / 2);
    bi_store.widget_builder_config.chart.pivot_columns = keys_map.non_value_keys.slice(0, half);
    bi_store.widget_builder_config.chart.pivot_rows = keys_map.non_value_keys.slice(half);

    const old_pivot_values = bi_store.widget_builder_config.chart?.pivot_values || [];
    const new_pivot_values = keys_map.value_keys.map(key => ({
      column: key,
      aggregation: old_pivot_values.find(val => val.column === key)?.aggregation || 'sum',
    }));
    bi_store.widget_builder_config.chart.pivot_values = new_pivot_values;
  }

  const { pivot_rows = [], pivot_columns = [], pivot_values = [] } = chart_config.value;

  const value_keys = pivot_values.map(val => val.column);

  const nested_headers = await generateNestedTableHeaders(
    state.data,
    pivot_columns,
    value_keys,
    pivot_rows,
    chart_config.value,
  );

  const leaf_columns = nested_headers.slice(-1)[0];
  state.table_columns = getTableColumns(leaf_columns, chart_config.value, columns_data_map.value);
  state.nested_headers = nested_headers;

  const nested_data = await generateHandsontableData(
    state.data,
    pivot_columns,
    pivot_values,
    pivot_rows,
    chart_config.value,
  );

  if (pivot_rows?.length)
    state.nested_rows = true;

  state.table_data = nested_data;
}

async function setupTable(reinitialize = true) {
  if (!props.isBuilder && props.isPreview && reinitialize) {
    const columns_map = state.columns.reduce((acc, col_key, index) => {
      const col_data = bi_store.widget_builder_config.chart.columns_map?.[col_key] || {};
      const default_order_index = 1000 * (index + 1);
      acc[col_key] = {
        key: col_key,
        width: get(col_data, 'width', null),
        visible: get(col_data, 'visible', true),
        order_index: get(col_data, 'order_index', default_order_index),
        label: get(col_data, 'label', col_key),
      };
      return acc;
    }, {});
    bi_store.widget_builder_config.chart.columns_map = cloneDeep(columns_map);
    bi_store.widget_builder_config.chart.column_groups = chart_config.value.column_groups.map((group) => {
      group.columns = group.columns.filter(col_key => columns_map[col_key]?.visible);
      return group;
    });
  }

  if (props.isBuilder) {
    state.table_columns = getTableColumns(state.columns, chart_config.value, columns_data_map.value);
  }
  else {
    const table_columns = orderBy(
      state.columns.filter(col =>
        get(chart_config.value?.columns_map || {}, `${col}.visible`, true),
      ),
      col => get(chart_config.value?.columns_map || {}, `${col}.order_index`, 0),
    );
    state.table_columns = getTableColumns(table_columns, chart_config.value, columns_data_map.value);
    state.nested_headers = generateTableGroupHeaders(chart_config.value);
  }

  state.table_data = state.data;
  if (props.isBuilder) {
    state.column_config = state.columns.reduce((acc, col) => {
      const column_details = bi_store.alias_to_column_mapping()[col];
      if (column_details?.is_aggregation) {
        acc[col] = { backgroundColor: '#FFFAEB' };
      }
      return acc;
    }, {});
  }
}

async function initializeTable(reinitialize = true) {
  state.data = props.data;
  state.columns = (props.widgetBuilderMetadata?.columns || [])?.map(col => col.name);

  if (chart_config.value?.type === 'pivot_table') {
    await setupPivotTable(reinitialize);
  }
  else {
    await setupTable(reinitialize);
  }
  bi_store.table_widget_config_change_detector = false;
}

function handleColumnResize(col_widths_map) {
  if (chart_config.value.type === 'table') {
    if (!props.isPreview) {
      const widget = bi_store.dashboards[bi_store.selected_dashboard]?.widgets?.find(w => w.widget_id === props.id);
      if (widget) {
        emitter.emit('ignoreNextConfigChange', props.id);
        const updated_config = cloneDeep(widget.config);
        Object.entries(col_widths_map).forEach(([col_key, width]) => {
          updated_config.chart.columns_map[col_key] = {
            ...updated_config.chart.columns_map[col_key],
            width,
          };
        });
        bi_store.updateWidget(props.id, { config: updated_config });
      }
      return;
    }
    Object.entries(col_widths_map).forEach(([col_key, width]) => {
      if (bi_store.widget_builder_config.chart.columns_map?.[col_key]) {
      // Update width in the columns map
        bi_store.widget_builder_config.chart.columns_map[col_key] = {
          ...bi_store.widget_builder_config.chart.columns_map[col_key],
          width,
        };
      }
    });
  }
}

function additionalTableSettings() {
  // if (chart_config.value?.type === 'pivot_table') {
  //   return {
  //     fixedRowsBottom: chart_config.value?.show_grand_totals ? 1 : 0,
  //   };
  // }
  if (props.isPreview || props.isBuilder) {
    return {
      viewportRowRenderingOffset: 100,
      viewportColumnRenderingOffset: 30,
    };
  }
  return {};
}

// Helper functions that use the composable with current context
function getCellFormattingWithContext(params) {
  return getCellFormatting({
    ...params,
    chart_config: chart_config.value,
    table_columns: state.table_columns,
    table_data: state.data,
  });
}

function autoFitColumnsWithContext(hot) {
  return autoFitColumns(hot, chart_config.value, state.table_columns);
}

watch(() => table_height.value, () => {
  if (state.table_instance?.hotInstance) {
    state.table_instance.hotInstance.updateSettings({
      height: table_height.value,
    });
  }
});

watch(() => (bi_store.fetching_widget_data === props.id) || (bi_store.fetching_widget_data === 'preview'), (val) => {
  if (state.table_instance) {
    const loading = state.table_instance.hotInstance.getPlugin('loading');
    if (val)
      loading.show();
    else
      loading.hide();
  }
});

watch(() => (bi_store.table_widget_config_change_detector), (val) => {
  if (!props.isPreview || !val)
    return;
  // state.changes_detected = true;
  initializeTable(false);
  state.force_update++;
}, { deep: true });
// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  initializeTable();
});
</script>

<template>
  <div class="h-[100%] w-full">
    <div
      v-if="state.changes_detected"
      class="flex items-center justify-center w-full h-full"
    >
      <!-- Popup card -->
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 flex flex-col items-center min-w-[600px]">
        <HawkFeaturedIcon theme="light-circle-outline" size="md" color="primary" class="mb-4">
          <IconHawkInfoCircle />
        </HawkFeaturedIcon>

        <div class="text-sm font-semibold mb-1">
          {{ $t('Changes detected') }}
        </div>

        <div class="text-sm text-gray-600 mb-6">
          {{ $t('New changes is been detected. You can reload to view new changes.') }}
        </div>

        <HawkButton :loading="state.loading_changes">
          <IconHawkRefreshCwTwo />
          <span class="text-sm font-medium">
            {{ $t('Load Preview') }}
          </span>
        </HawkButton>
      </div>
    </div>
    <div v-else-if="!state.table_data?.length" class="h-full w-full">
      <div class="h-full w-full flex flex-col justify-center items-center text-center">
        <IconIllustrationBiEmptyDataBuilder />
        <div class="text-sm font-semibold text-gray-900 mt-4 mb-1">
          No data to display
        </div>
        <div class="text-sm font-normal text-gray-600">
          Choose the columns on the left side to build the query and see the results
        </div>
      </div>
    </div>
    <BiHandsontable
      v-else
      :key="state.force_update"
      :bi-table-id="id"
      :height="table_height"
      :data="state.table_data"
      :columns="state.table_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :additional-table-settings="additionalTableSettings()"
      :show-skeleton-loader="state.loading"
      :get-cell-formatting="getCellFormattingWithContext"
      :row-headers="enable_row_headers"
      class="h-full border"
      :enable-column-sorting="chart_config.type !== 'pivot_table'"
      :get-custom-cell-content="getCustomCellContent"
      @table-instance="state.table_instance = $event"
      @after-load-data="autoFitColumnsWithContext"
      @column-resize="handleColumnResize"
    />
  </div>
</template>
