import { orderBy } from 'lodash-es';
import { nextTick } from 'vue';
import { useTableConditionalFormatter } from '~/bi/components/chart-builder/tabs/table-tabs/conditional-formatting/composables/bi-conditional-formatter.composable.js';
import { useBiDataFormatter } from '~/bi/components/widgets/table-widget/composables/bi-table-data-formatter.composable.js';

export function useBiTableHelper() {
  const { checkRuleMatch, getContrastTextColor, getLighterColor, getColorForValue } = useTableConditionalFormatter();
  const { formatValue, getCustomCellContent, isHtmlFormatted, aggregateValues } = useBiDataFormatter();

  // Parse a value to a safe number, returning NaN for invalid values
  function parseSafeNumber(value) {
    if (value === null || value === undefined || value === '') {
      return Number.NaN;
    }
    const num = Number(value);
    return Number.isNaN(num) ? Number.NaN : num;
  }

  // Get min and max values for a specific field from the table instance
  function getMinMax(rule, table_data) {
    /**
     * NOTE: LOGIC VALUES CALCULATED FROM HANDSONTABLE using parameters (instance, rule, table_columns)
     const columns = table_columns.map((col, index) => ({ index, field: col.field })).filter(col => col.field === rule.field);
     let values = [];
     for (const col of columns) {
       values = values.concat(instance.getDataAtCol(col.index).map(val => parseSafeNumber(val)).filter(val => !Number.isNaN(val)));
      }
     */
    const values = table_data.map(value => value[rule.field]).map(parseSafeNumber).filter(num => !Number.isNaN(num));
    if (values.length) {
      return {
        min: Math.min(...values),
        max: Math.max(...values),
      };
    }
    return null;
  }

  //  Get cell formatting based on conditional formatting rules
  function getCellFormatting({ column, row_data, value, chart_config, table_data }) {
    // For total rows and columns styles
    if (row_data.__is_grand_total) {
      return {
        'background-color': '#475467',
        'font-weight': 600,
        'color': '#FFFFFF',
      };
    }
    if (row_data.__is_column_total || column?.data?.includes('__row_total_')) {
      return {
        'background-color': '#EAECF0',
        'font-weight': 600,
        'color': '#101828 !important',
      };
    }

    let rules = [];
    if (chart_config?.type) {
      rules = chart_config?.conditional_formatting || [];
    }
    const min_max_map = {};
    for (const rule of rules) {
      if (rule.formatting_style === 'single_color') {
        if (checkRuleMatch(rule, row_data, column, value)) {
          let color = rule.color;
          if (column.field !== rule.field) {
            color = getLighterColor(rule.color);
          }
          return {
            'background-color': color,
            'color': getContrastTextColor(color),
          };
        }
      }
      else {
        if (column.field !== rule.field)
          continue;
        let min = rule.start_range_value;
        let max = rule.end_range_value;
        if ((rule.start_range_at === 'min' || rule.end_range_at === 'max') && !min_max_map[column.field]) {
          min_max_map[rule.field] = getMinMax(rule, table_data);
        }
        if (rule.start_range_at === 'min') {
          min = min_max_map[rule.field]?.min;
        }
        if (rule.end_range_at === 'max') {
          max = min_max_map[rule.field]?.max;
        }
        const cell_value = parseSafeNumber(value);
        if (Number.isNaN(cell_value) || (cell_value < min || cell_value > max))
          continue;
        const color = getColorForValue(cell_value, min, max, rule.color, rule.color_range);
        return {
          'background-color': color,
          'color': getContrastTextColor(color),
        };
      }
    }
    return null;
  }

  // Auto-fit columns in the Handsontable instance
  function autoFitColumns(hot, chart_config, table_columns) {
    nextTick(() => {
      const plugin = hot.getPlugin('autoColumnSize');
      plugin.recalculateAllColumnsWidth();
      const widths = [];
      for (let col = 0; col < hot.countCols(); col++) {
        let width = null;
        if (chart_config?.type === 'table') {
          const column_key = table_columns[col].data;
          width = chart_config?.columns_map?.[column_key]?.width || plugin.getColumnWidth(col);
        }
        else {
          width = plugin.getColumnWidth(col);
        }
        width = Math.max(width, 150);
        widths.push(width);
      }

      if (widths.length) {
        setTimeout(() => {
          hot.updateSettings({ colWidths: widths });
          hot.render();
        }, 10);
      }
    });
  }

  // Generate nested headers for pivot tables
  async function generateNestedTableHeaders(data, columns, values, group_by_keys, chart_config, delimiter = '|') {
    const nested_headers = [];

    // Handle case: no column pivoting
    if (columns.length === 0) {
      const flat_header_row = [];

      // Add row headers
      for (const rh of group_by_keys) {
        flat_header_row.push({ label: rh, key: rh, field: rh });
      }

      // Add values directly as columns
      for (const val of values) {
        flat_header_row.push({ label: val, key: val, field: val });
      }

      if (chart_config?.show_row_totals) {
        for (const val of values) {
          flat_header_row.push({ label: `Total ${val}`, key: `__row_total_${val}` });
        }
      }

      nested_headers.push(flat_header_row);
      return nested_headers;
    }

    // Build tree structure for column pivoting
    function buildTree(data, level = 0) {
      if (level >= columns.length)
        return [];

      const groups = {};
      for (const row of data) {
        const key = row[columns[level]];
        if (!groups[key])
          groups[key] = [];
        groups[key].push(row);
      }

      const nodes = [];
      for (const key in groups) {
        const children = buildTree(groups[key], level + 1);
        nodes.push({ label: key, children });
      }
      return nodes;
    }

    const tree = buildTree(data);

    function countLeaves(node) {
      if (!node.children || node.children.length === 0)
        return 1;
      return node.children.reduce((sum, child) => sum + countLeaves(child), 0);
    }

    // Generate nested headers
    function fillHeaders(nodes, level = 0) {
      if (!nested_headers[level]) {
        nested_headers[level] = [];
        for (let i = 0; i < group_by_keys.length; i++) {
          nested_headers[level].push({ label: '', colspan: 1 });
        }
      }

      for (const node of nodes) {
        const leaf_count = countLeaves(node);
        nested_headers[level].push({
          label: node.label,
          colspan: leaf_count * values.length,
        });

        if (node.children.length > 0) {
          fillHeaders(node.children, level + 1);
        }
      }
    }

    fillHeaders(tree);

    const final_row = [];
    for (const rh of group_by_keys) {
      final_row.push({ label: rh, key: rh, field: rh });
    }

    function pushLeafLabels(nodes, path = []) {
      for (const node of nodes) {
        const currentPath = [...path, node.label];
        if (node.children.length > 0) {
          pushLeafLabels(node.children, currentPath);
        }
        else {
          for (const val of values) {
            final_row.push({
              label: val,
              key: currentPath.concat(val).join(delimiter),
              field: val,
            });
          }
        }
      }
    }

    pushLeafLabels(tree);
    if (chart_config?.show_row_totals) {
      for (let i = 1; i <= nested_headers.length; i++) {
        nested_headers[i - 1].push({ label: i === nested_headers.length ? 'Row Totals' : '', colspan: values.length });
      }
      for (const val of values) {
        final_row.push({
          label: `Total ${val}`,
          key: `__row_total_${val}`,
        });
      }
    }
    nested_headers.push(final_row);
    return nested_headers;
  }

  // Generate Handsontable data for pivot tables
  function generateHandsontableData(data, columns, values, group_by_keys, chart_config, delimiter = '|') {
    // Helper: group rows recursively
    function normalizeKey(val) {
      // Consistent key format for grouping — handles undefined/null gracefully
      return val === undefined || val === null ? '' : String(val);
    }

    function getRowKeys(row) {
      const column_key = columns.length
        ? columns.map(col => normalizeKey(row[col])).join(delimiter)
        : '';
      const row_keys = [];
      for (const { column } of values) {
        if (column_key.length) {
          row_keys.push({ data_key: `${column_key}${delimiter}${column}`, value_key: column });
        }
        else {
          row_keys.push({ data_key: column, value_key: column });
        }
      }
      return row_keys;
    }

    function formatRow(row, node = {}) {
      const row_keys = getRowKeys(row);
      for (const key of row_keys) {
        node[key.data_key] = row[key.value_key] ?? null;
        node[key.value_key] = node[key.data_key];
      }
      node.__actual_row = row;

      if (chart_config?.show_row_totals) {
        for (const { column } of values) {
          node[`__row_total_${column}`] = row[column] ?? null;
        }
      }

      return node;
    }

    function groupByHeaders(rows, headers) {
      if (headers.length === 0) {
        return rows.map(row => formatRow(row));
      }

      const [current, ...rest] = headers;
      const groups = {};

      rows.forEach((row) => {
        const key = normalizeKey(row[current]);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(row);
      });

      return Object.entries(groups).map(([key, grouped_rows]) => {
        const node = { [current]: key };

        if (rest.length > 0) {
          node.__is_group = true;
          node.__is_group_column = current;
          node.__children = groupByHeaders(grouped_rows, rest);

          if (chart_config?.show_column_totals || chart_config?.show_row_totals) {
            // Collect values for aggregation
            const column_values = {};
            const row_total_values = {};

            for (const row of grouped_rows) {
              if (chart_config?.show_column_totals) {
                const row_keys = getRowKeys(row);
                for (const key of row_keys) {
                  if (!column_values[key.data_key])
                    column_values[key.data_key] = [];
                  column_values[key.data_key].push(row[key.value_key]);
                }
              }

              if (chart_config?.show_row_totals) {
                for (const { column } of values) {
                  if (!row_total_values[column])
                    row_total_values[column] = [];
                  row_total_values[column].push(row[column]);
                }
              }
            }

            // Apply aggregation
            if (chart_config?.show_column_totals) {
              for (const [data_key, vals] of Object.entries(column_values)) {
                const value_config = values.find(v => data_key.includes(v.column));
                const aggregation = value_config?.aggregation || 'sum';
                node[data_key] = String(aggregateValues(vals, aggregation) ?? 0);
              }
              node.__is_column_total = true;
            }

            if (chart_config?.show_row_totals) {
              for (const { column, aggregation } of values) {
                const agg_type = aggregation || 'sum';
                node[`__row_total_${column}`] = String(aggregateValues(row_total_values[column], agg_type) ?? 0);
              }
            }
          }
        }
        else {
          node.__is_leaf_group = true;

          for (const row of grouped_rows) {
            formatRow(row, node);
          }
        }

        return node;
      });
    }

    const nested_data = groupByHeaders(data, group_by_keys);

    // --- Grand Total Column ---
    if (chart_config?.show_grand_totals) {
      const grand_total = { [group_by_keys[0] || 'Total']: 'Grand Totals', __is_grand_total: true };
      const column_values = {};
      const row_total_values = {};

      for (const row of data) {
        // collect per-column values
        const row_keys = getRowKeys(row);
        for (const key of row_keys) {
          if (!column_values[key.data_key])
            column_values[key.data_key] = [];
          column_values[key.data_key].push(row[key.value_key]);
        }

        // collect per-row totals
        for (const { column } of values) {
          if (!row_total_values[column])
            row_total_values[column] = [];
          row_total_values[column].push(row[column]);
        }
      }

      // Apply aggregation to column values
      for (const [data_key, vals] of Object.entries(column_values)) {
        const value_config = values.find(v => data_key.includes(v.column));
        const aggregation = value_config?.aggregation || 'sum';
        grand_total[data_key] = String(aggregateValues(vals, aggregation) ?? 0);
      }

      // Apply aggregation to row totals
      for (const { column, aggregation } of values) {
        const agg_type = aggregation || 'sum';
        grand_total[`__row_total_${column}`] = String(aggregateValues(row_total_values[column], agg_type) ?? 0);
      }

      nested_data.push(grand_total);
    }

    return nested_data;
  }

  // Generate table columns configuration for Handsontable
  function getTableColumns(columns, chart_config, columns_data_map) {
    return columns.map((col) => {
      const is_table = chart_config?.type === 'table';

      let columns_map = {};

      if (is_table) {
        columns_map = chart_config?.columns_map || {};
      }

      // Handle the case when `col` is a string
      if (typeof col === 'string') {
        const label = columns_map?.[col]?.label || col;
        const width = columns_map?.[col]?.width ?? null;
        const data_type = columns_data_map[col]?.type || 'text';

        return {
          label,
          data: col,
          type: data_type === 'boolean' ? 'checkbox' : 'text',
          readOnly: true,
          field: col,
          data_type,
          has_custom_content: isHtmlFormatted(data_type),
          ...(width && { width }),
        };
      }

      // When `col` is an object
      const key = col.key || col.label;
      const field = col.field || key;
      const label = columns_map?.[field]?.label || col.label;
      const width = columns_map?.[key]?.width ?? null;
      const data_type = columns_data_map[field]?.type || 'text';

      return {
        label,
        data: key,
        type: data_type === 'boolean' ? 'checkbox' : 'text',
        readOnly: true,
        field,
        data_type,
        has_custom_content: isHtmlFormatted(data_type),
        ...(width && { width }),
      };
    });
  }

  // Generate nested headers for table with column groups
  function generateTableGroupHeaders(chart_config) {
    if (chart_config?.type !== 'table' || !chart_config?.column_groups?.length) {
      return [];
    }

    if (chart_config.column_groups?.every(group => group.columns?.length === 0)) {
      return [];
    }

    const first_level = [];
    const second_level = [];
    const columns_map = chart_config.columns_map || {};
    const column_groups = chart_config.column_groups || [];

    // Create ordered list including groups and individual columns
    const ordered_items = orderBy([
      ...Object.values(columns_map),
      ...column_groups,
    ], 'order_index');

    // Track which columns are already in groups
    const grouped_columns = new Set();
    column_groups.forEach((group) => {
      group.columns?.forEach(col => grouped_columns.add(col));
    });

    ordered_items.forEach((item) => {
      if (item.type === 'group') {
        const visible_columns = item.columns.filter(col => columns_map[col]?.visible);
        // Add group header with colspan
        if (visible_columns?.length) {
          first_level.push({
            label: item.label,
            colspan: visible_columns?.length || 1,
          });
        }

        // Add individual column headers under the group
        visible_columns?.forEach((col_key) => {
          const column = columns_map[col_key];
          second_level.push({
            label: column?.label || col_key,
            colspan: 1,
          });
        });
      }
      else if (!grouped_columns.has(item.key)) {
        // Regular column not in any group
        first_level.push({
          label: '',
          colspan: 1,
        });

        second_level.push({
          label: item.label,
          colspan: 1,
        });
      }
    });

    return [first_level, second_level];
  }

  return {
    parseSafeNumber,
    getMinMax,
    getCellFormatting,
    autoFitColumns,
    generateNestedTableHeaders,
    generateTableGroupHeaders,
    generateHandsontableData,
    getTableColumns,
    getCustomCellContent,
    formatValue,
    isHtmlFormatted,
  };
}
