<script setup>
const emit = defineEmits(['close']);
</script>

<template>
  <HawkModalContainer :options="{ escToClose: false }" content_class="h-full w-full rounded-none">
    <slot>
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            <slot name="title" />
          </template>
        </HawkModalHeader>
        <HawkModalContent class="!p-0 max-h-[calc(100vh-83px)]">
          <div class="flex">
            <div class="w-1/4 py-4 px-4 h-[calc(100vh-83px)] border-r scrollbar">
              <slot name="left-content" />
            </div>
            <div class="h-[calc(100vh-83px)] w-3/4">
              <slot name="right-content" />
            </div>
          </div>
        </HawkModalContent>
      </div>
    </slot>
  </HawkModalContainer>
</template>
