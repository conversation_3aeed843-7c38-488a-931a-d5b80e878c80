<script setup>
// --------------------------------- Imports -------------------------------- //
import { useBiStore } from '~/bi/store/bi.store';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';

const {
  $t,
  $toast,
} = useCommonImports();
const draggable = defineAsyncComponent(() => import('vuedraggable'));

// ---------------------------------- Props --------------------------------- //

// ---------------------------------- Emits --------------------------------- //

// ---------------------------- Injects/Provides ---------------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //
const bi_store = useBiStore();
const pre_defined_filter_name = 'New Filter';

// ------------------------ Variables - Local - refs ------------------------ //
const current_selected_filter_name = ref(bi_store.get_global_filters[0]?.general?.filter_name || pre_defined_filter_name);

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const global_filters = computed(() => bi_store.get_global_filters);

// -------------------------------- Functions ------------------------------- //
function hasFilterWithSameName(filters, filter_name) {
  return filters.some(filter => filter.general?.filter_name === filter_name);
}
function addFilter() {
  console.log('addFilter');

  if (hasFilterWithSameName(bi_store.get_global_filters, pre_defined_filter_name)) {
    $toast({ text: $t('A filter with the same name "New Filter" already exists'), type: 'error' });
    return;
  }
  const pre_defined_filter = {
    general: {
      filter_name: pre_defined_filter_name,
      filter_type: 'text',
      dataset: null,
      column: null,
      filter_settings: {},
    },
    widgets: {},
  };

  bi_store.addGlobalFilters(pre_defined_filter);
}
function onReorder() {
  console.log('onReorder');
}
// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
onMounted(() => {
  if (global_filters.value.length === 0) {
    addFilter();
  };
});
</script>

<template>
  <div class="border border-gray-300 bg-gray-100 rounded-md w-1/2">
    <div class="flex flex-col gap-2">
      <div class="scrollbar h-[330px]">
        <draggable v-model="global_filters" item-key="general.filter_name" tag="div" handle=".move" :animation="150" @change="onReorder">
          <template #item="{ element: field, index }">
            <div
              class="group p-2 rounded-md mx-2 my-1 cursor-pointer"
              :class="{ 'bg-gray-700': current_selected_filter_name === field.general?.filter_name, 'hover:bg-gray-200': current_selected_filter_name !== field.general?.filter_name }"
              @click="current_selected_filter_name = field.general?.filter_name"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="pr-3 move cursor-move invisible group-hover:visible">
                    <IconHawkDragIcon class="text-gray-400 w-[12px] h-[12px]" :class="{ 'text-white ': current_selected_filter_name === field.general?.filter_name }" />
                  </div>
                  <HawkText :length="30" class="text-gray-700" :class="{ 'text-white font-semibold': current_selected_filter_name === field.general?.filter_name }" :content="field.general?.filter_name" />
                </div>
                <div class="text-gray-500 hidden group-hover:block mr-2">
                  <IconHawkTrashThree class="text-gray-500 w-[18px] h-[18px]" :class="{ 'text-white ': current_selected_filter_name === field.general?.filter_name }" @click.stop />
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
      <HawkButton type="link" class="ml-2" color="gray" @click="addFilter">
        <IconHawkPlus />
        <span>
          {{ $t('Add filter') }}
        </span>
      </HawkButton>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
