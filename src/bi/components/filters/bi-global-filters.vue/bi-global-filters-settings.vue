<script setup>
// --------------------------------- Imports -------------------------------- //
import GeneralSettingForm from '~/bi/components/filters/bi-global-filters.vue/bi-global-filters-setting-tab/general-form.vue';
import WidgetsMapping from '~/bi/components/filters/bi-global-filters.vue/bi-global-filters-setting-tab/widgets-mapping.vue';
import BIGlobalFiltersSidebar from '~/bi/components/filters/bi-global-filters.vue/bi-global-filters-sidebar.vue';

import { useCommonImports } from '~/common/composables/common-imports.composable.js';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({});

// ---------------------------------- Emits --------------------------------- //
const emit = defineEmits(['close', 'save']);

// ---------------------------- Injects/Provides ---------------------------- //

// ----------------------- Variables - Pinia - consts ----------------------- //
const {
  $t,
} = useCommonImports();

// --------------------- Variables - Pinia - storeToRefs -------------------- //

// ------------------- Variables - Local - consts and lets ------------------ //

// ------------------------ Variables - Local - refs ------------------------ //
const active_item = ref('general');

// ---------------------- Variables - Local - reactives --------------------- //

// --------------------------- Computed properties -------------------------- //
const tabs = computed(() => [{
  uid: 'general',
  label: $t('General'),
  disabled: false,
}, {
  uid: 'widgets',
  label: $t('Widgets'),
  disabled: false,
}]);

// -------------------------------- Functions ------------------------------- //
function onChangeTab(value) {
  console.log('onChangeTab', value);
  active_item.value = value.uid;
}

function onSave() {
  emit('save', {});
  console.log('SAVE payload WIP:::');
}

// -------------------------------- Watchers -------------------------------- //

// ----------------------------- Lifecycle Hooks ---------------------------- //
</script>

<template>
  <HawkModalContainer content_class="rounded-lg w-[800px]">
    <div>
      <HawkModalHeader @close="emit('close')">
        <template #header>
          <div
            class="flex items-start p-6 border-b border-b-gray-200 justify-between text-lg font-semibold text-gray-800"
          >
            <div class="flex items-start">
              <div class="flex flex-col justify-start">
                {{ $t('Configure filters') }}
              </div>
            </div>
            <div class="flex font-normal items-center justify-center -m-2">
              <div
                class="text-gray-600 rounded-md cursor-pointer flex justify-center items-center p-2 ml-3 hover:bg-gray-50"
                @click="$emit('close')"
              >
                <IconHawkXClose class="w-6 h-6 text-gray-500 hover:text-gray-900" />
              </div>
            </div>
          </div>
        </template>
      </HawkModalHeader>
      <HawkModalContent :is_scroll="false">
        <div class="h-96 flex gap-4">
          <!-- Left side filter list -->
          <BIGlobalFiltersSidebar />
          <!-- Right side filter config -->
          <div class="w-full">
            <HawkTabs :tabs="tabs" :active_item="active_item" @tab-click="onChangeTab" />

            <!-- General config -->
            <div v-if="active_item === 'general'" class="mt-2">
              <GeneralSettingForm />
            </div>
            <!-- Widgets config -->
            <div v-if="active_item === 'widgets'" class="mt-2">
              <WidgetsMapping />
            </div>
          </div>
        </div>
      </HawkModalContent>
      <HawkModalFooter>
        <template #right>
          <div class="flex items-center justify-end">
            <HawkButton
              class="mr-3"
              type="outlined"
              @click="$emit('close')"
            >
              {{ $t('Cancel') }}
            </HawkButton>
            <HawkButton
              color="primary"
              @click="onSave"
            >
              {{ $t('Save') }}
            </HawkButton>
          </div>
        </template>
      </HawkModalFooter>
    </div>
  </HawkModalContainer>
</template>
